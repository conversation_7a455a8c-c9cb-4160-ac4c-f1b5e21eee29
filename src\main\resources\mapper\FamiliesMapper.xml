<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.barantt.familyclender.dao.mapper.FamiliesMapper">

  <resultMap id="BaseResultMap" type="com.barantt.familyclender.dao.pojo.Families">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, name, created_at, updated_at
  </sql>

  <!-- 插入家庭信息 -->
  <insert id="insertFamily" parameterType="com.barantt.familyclender.dao.pojo.Families" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO families (name, created_at, updated_at)
    VALUES (#{name,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>

  <!-- 根据名称查询家庭 -->
  <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM families
    WHERE name = #{name,jdbcType=VARCHAR}
  </select>
</mapper>
