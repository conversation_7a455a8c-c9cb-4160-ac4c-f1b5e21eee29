<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.barantt.familyclender.dao.mapper.FamiliesMapper">

  <resultMap id="BaseResultMap" type="com.barantt.familyclender.dao.pojo.Families">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, name, created_at, updated_at
  </sql>
</mapper>
