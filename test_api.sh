#!/bin/bash

# 家庭日历系统API测试脚本
# 使用方法: ./test_api.sh

BASE_URL="http://localhost:8080"

echo "=== 家庭日历系统API测试 ==="
echo

# 1. 创建家庭
echo "1. 创建家庭..."
FAMILY_RESPONSE=$(curl -s -X POST $BASE_URL/families \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试家庭"
  }')

echo "响应: $FAMILY_RESPONSE"

# 提取家庭ID (简单的JSON解析，实际项目中建议使用jq)
FAMILY_ID=$(echo $FAMILY_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "创建的家庭ID: $FAMILY_ID"
echo

# 2. 创建家庭成员
echo "2. 创建家庭成员..."

echo "2.1 创建成员: 张三"
MEMBER1_RESPONSE=$(curl -s -X POST $BASE_URL/family-members \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"name\": \"张三\"
  }")
echo "响应: $MEMBER1_RESPONSE"

MEMBER1_ID=$(echo $MEMBER1_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "创建的成员ID: $MEMBER1_ID"
echo

echo "2.2 创建成员: 李四"
MEMBER2_RESPONSE=$(curl -s -X POST $BASE_URL/family-members \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"name\": \"李四\"
  }")
echo "响应: $MEMBER2_RESPONSE"

MEMBER2_ID=$(echo $MEMBER2_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "创建的成员ID: $MEMBER2_ID"
echo

echo "2.3 创建成员: 王五"
MEMBER3_RESPONSE=$(curl -s -X POST $BASE_URL/family-members \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"name\": \"王五\"
  }")
echo "响应: $MEMBER3_RESPONSE"

MEMBER3_ID=$(echo $MEMBER3_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "创建的成员ID: $MEMBER3_ID"
echo

# 3. 查询家庭成员列表
echo "3. 查询家庭成员列表..."
MEMBERS_LIST_RESPONSE=$(curl -s -X GET $BASE_URL/family-members/family/$FAMILY_ID)
echo "响应: $MEMBERS_LIST_RESPONSE"
echo

# 4. 查询单个家庭成员
echo "4. 查询单个家庭成员..."
MEMBER_DETAIL_RESPONSE=$(curl -s -X GET $BASE_URL/family-members/$MEMBER1_ID)
echo "响应: $MEMBER_DETAIL_RESPONSE"
echo

# 5. 删除家庭成员
echo "5. 删除家庭成员..."
DELETE_RESPONSE=$(curl -s -X DELETE $BASE_URL/family-members/$MEMBER2_ID)
echo "响应: $DELETE_RESPONSE"
echo

# 6. 再次查询家庭成员列表，验证删除效果
echo "6. 验证删除效果 - 再次查询家庭成员列表..."
MEMBERS_LIST_AFTER_DELETE=$(curl -s -X GET $BASE_URL/family-members/family/$FAMILY_ID)
echo "响应: $MEMBERS_LIST_AFTER_DELETE"
echo

# 7. 测试错误情况
echo "7. 测试错误情况..."

echo "7.1 创建成员时使用无效的家庭ID"
ERROR_RESPONSE1=$(curl -s -X POST $BASE_URL/family-members \
  -H "Content-Type: application/json" \
  -d '{
    "familyId": 99999,
    "name": "测试用户"
  }')
echo "响应: $ERROR_RESPONSE1"
echo

echo "7.2 创建成员时使用空姓名"
ERROR_RESPONSE2=$(curl -s -X POST $BASE_URL/family-members \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"name\": \"\"
  }")
echo "响应: $ERROR_RESPONSE2"
echo

echo "7.3 删除不存在的成员"
ERROR_RESPONSE3=$(curl -s -X DELETE $BASE_URL/family-members/99999)
echo "响应: $ERROR_RESPONSE3"
echo

echo "7.4 查询不存在的成员"
ERROR_RESPONSE4=$(curl -s -X GET $BASE_URL/family-members/99999)
echo "响应: $ERROR_RESPONSE4"
echo

echo "=== 测试完成 ==="
