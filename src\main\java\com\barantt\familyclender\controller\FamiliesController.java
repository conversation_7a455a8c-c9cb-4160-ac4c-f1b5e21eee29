package com.barantt.familyclender.controller;

import com.barantt.familyclender.common.Result;
import com.barantt.familyclender.dao.pojo.Families;
import com.barantt.familyclender.dto.CreateFamilyRequest;
import com.barantt.familyclender.service.IFamiliesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *  前端控制器
 *
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
@RestController
@RequestMapping("/families")
public class FamiliesController {

    @Autowired
    private IFamiliesService familiesService;

    /**
     * 新增家庭
     *
     * @param request 创建家庭请求
     * @return 创建结果
     */
    @PostMapping
    public Result<Families> createFamily(@RequestBody CreateFamilyRequest request) {
        try {
            Families family = familiesService.createFamily(request.getName());
            return Result.success("家庭创建成功", family);
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("创建家庭失败：" + e.getMessage());
        }
    }
}
