# 家庭日历系统

## 项目简介
家庭日历系统是一个用于管理家庭成员和家庭事件的Web应用程序。

## 技术栈
- Spring Boot 2.6.13
- MyBatis + tk.mybatis
- MySQL 8.0
- Lombok
- Hutool

## 项目结构
```
src/
├── main/
│   ├── java/
│   │   └── com/barantt/familyclender/
│   │       ├── common/          # 通用类
│   │       ├── controller/      # 控制器层
│   │       ├── dao/            # 数据访问层
│   │       │   ├── mapper/     # MyBatis Mapper接口
│   │       │   └── pojo/       # 实体类
│   │       ├── dto/            # 数据传输对象
│   │       └── service/        # 服务层
│   │           └── impl/       # 服务实现
│   └── resources/
│       ├── mapper/             # MyBatis XML映射文件
│       └── application.properties
└── test/                       # 测试代码
```

## 功能特性

### 已实现功能
- ✅ 新增家庭
- ✅ 新增家庭成员
- ✅ 删除家庭成员
- ✅ 查询家庭成员列表
- ✅ 查询单个家庭成员
- ✅ 创建事件
- ✅ 更新事件
- ✅ 删除事件
- ✅ 查询事件详情
- ✅ 查询家庭事件列表
- ✅ 按日期范围查询事件（按天分组）

### 计划功能
- ⏳ 查询家庭列表
- ⏳ 修改家庭信息
- ⏳ 删除家庭
- ⏳ 修改家庭成员信息
- ⏳ 事件提醒功能
- ⏳ 事件重复设置

## 快速开始

### 1. 环境要求
- JDK 1.8+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库配置
1. 创建数据库：
```sql
CREATE DATABASE familyclender DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行建表脚本：
```bash
mysql -u root -p familyclender < database_schema.sql
```

3. 修改 `application.properties` 中的数据库连接信息：
```properties
spring.datasource.url=********************************************************************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### 3. 启动应用
```bash
mvn spring-boot:run
```

应用将在 http://localhost:8080/api 启动

### 4. 测试API

#### 新增家庭
```bash
curl -X POST http://localhost:8080/api/families \
  -H "Content-Type: application/json" \
  -d '{
    "name": "我的家庭"
  }'
```

#### 新增家庭成员
```bash
curl -X POST http://localhost:8080/api/family-members \
  -H "Content-Type: application/json" \
  -d '{
    "familyId": 1,
    "name": "张三"
  }'
```

#### 查询家庭成员列表
```bash
curl -X GET http://localhost:8080/api/family-members/family/1
```

#### 删除家庭成员
```bash
curl -X DELETE http://localhost:8080/api/family-members/1
```

#### 创建事件
```bash
curl -X POST http://localhost:8080/api/events \
  -H "Content-Type: application/json" \
  -d '{
    "familyId": 1,
    "title": "家庭聚餐",
    "description": "周末家庭聚餐活动",
    "location": "家里",
    "startTime": "2025-07-03 18:00:00",
    "endTime": "2025-07-03 20:00:00",
    "memberIds": [1, 2]
  }'
```

#### 更新事件
```bash
curl -X PUT http://localhost:8080/api/events/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的家庭聚餐",
    "location": "餐厅"
  }'
```

#### 查询事件详情
```bash
curl -X GET http://localhost:8080/api/events/1
```

#### 查询家庭事件列表
```bash
curl -X GET http://localhost:8080/api/events/family/1
```

#### 删除事件
```bash
curl -X DELETE http://localhost:8080/api/events/1
```

#### 按日期范围查询事件
```bash
# 查询一周事件
curl -X GET "http://localhost:8080/api/events/family/1/date-range?startDate=2025-07-07&endDate=2025-07-13"

# 查询单天事件
curl -X GET "http://localhost:8080/api/events/family/1/date-range?startDate=2025-07-12&endDate=2025-07-12"
```

## API文档
详细的API文档请参考 [API_DOCUMENTATION.md](API_DOCUMENTATION.md)

## 运行测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=FamiliesServiceTest
```

## 开发说明

### 代码规范
- 使用Lombok减少样板代码
- 统一使用Result类包装API响应
- 遵循RESTful API设计规范
- 使用@Autowired进行依赖注入

### 异常处理
- 参数校验异常返回400状态码
- 业务异常返回500状态码
- 统一的错误响应格式

### 数据库设计
- 使用AUTO_INCREMENT主键
- 统一的created_at和updated_at时间戳
- 适当的外键约束和索引

## 贡献指南
1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证
MIT License
