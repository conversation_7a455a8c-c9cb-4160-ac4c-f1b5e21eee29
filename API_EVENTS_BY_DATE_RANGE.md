# 按日期范围查询事件接口文档

## 接口描述
根据家庭ID和时间范围查询事件，返回按日期分组的结果。该接口会返回指定日期范围内每一天的事件列表，即使某天没有事件也会返回空列表。

## 请求信息
- **URL**: `/api/events/family/{familyId}/date-range`
- **Method**: `GET`

## 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| familyId | Integer | 是 | 家庭ID，必须大于0 |

## 查询参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | String | 是 | 开始日期，格式：yyyy-MM-dd |
| endDate | String | 是 | 结束日期，格式：yyyy-MM-dd |

## 响应格式
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "date": "2025-07-07",
            "eventCount": 2,
            "events": [
                {
                    "id": 1,
                    "familyId": 1,
                    "title": "工作日早餐",
                    "description": "全家一起吃早餐",
                    "location": "家里餐厅",
                    "startTime": "2025-07-07T07:00:00",
                    "endTime": "2025-07-07T07:30:00",
                    "createdAt": "2025-07-02T10:30:00",
                    "updatedAt": "2025-07-02T10:30:00"
                },
                {
                    "id": 2,
                    "familyId": 1,
                    "title": "爸爸加班",
                    "description": "项目紧急，需要加班",
                    "location": "公司",
                    "startTime": "2025-07-07T19:00:00",
                    "endTime": "2025-07-07T22:00:00",
                    "createdAt": "2025-07-02T10:31:00",
                    "updatedAt": "2025-07-02T10:31:00"
                }
            ]
        },
        {
            "date": "2025-07-08",
            "eventCount": 1,
            "events": [
                {
                    "id": 3,
                    "familyId": 1,
                    "title": "瑜伽课",
                    "description": "妈妈的瑜伽课程",
                    "location": "健身房",
                    "startTime": "2025-07-08T19:00:00",
                    "endTime": "2025-07-08T20:30:00",
                    "createdAt": "2025-07-02T10:32:00",
                    "updatedAt": "2025-07-02T10:32:00"
                }
            ]
        },
        {
            "date": "2025-07-09",
            "eventCount": 0,
            "events": []
        }
    ]
}
```

## 响应参数说明
| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| message | String | 响应消息 |
| data | Array | 按日期分组的事件数据 |
| data[].date | String | 日期，格式：yyyy-MM-dd |
| data[].eventCount | Integer | 该日期的事件数量 |
| data[].events | Array | 该日期的事件列表 |
| data[].events[].id | Integer | 事件ID |
| data[].events[].familyId | Integer | 家庭ID |
| data[].events[].title | String | 事件标题 |
| data[].events[].description | String | 事件描述 |
| data[].events[].location | String | 事件地点 |
| data[].events[].startTime | String | 开始时间 |
| data[].events[].endTime | String | 结束时间 |
| data[].events[].createdAt | String | 创建时间 |
| data[].events[].updatedAt | String | 更新时间 |

## 特性说明
1. **完整日期覆盖**：返回指定日期范围内每一天的数据，即使某天没有事件
2. **按日期分组**：事件按开始时间的日期进行分组
3. **时间排序**：每天内的事件按开始时间升序排列
4. **事件计数**：每天都包含该天的事件数量统计

## 请求示例

### 查询一周事件
```bash
curl -X GET "http://localhost:8080/api/events/family/1/date-range?startDate=2025-07-07&endDate=2025-07-13"
```

### 查询单天事件
```bash
curl -X GET "http://localhost:8080/api/events/family/1/date-range?startDate=2025-07-12&endDate=2025-07-12"
```

### 查询工作日事件
```bash
curl -X GET "http://localhost:8080/api/events/family/1/date-range?startDate=2025-07-07&endDate=2025-07-11"
```

## 错误响应

### 参数错误
```json
{
    "code": 400,
    "message": "开始日期不能晚于结束日期",
    "data": null
}
```

### 家庭不存在
```json
{
    "code": 400,
    "message": "家庭ID不能为空且必须大于0",
    "data": null
}
```

### 日期格式错误
HTTP状态码：400 Bad Request
```json
{
    "timestamp": "2025-07-02T10:30:00.000+00:00",
    "status": 400,
    "error": "Bad Request",
    "message": "Failed to convert value of type 'java.lang.String' to required type 'java.util.Date'"
}
```

## 使用场景
1. **日历视图**：为前端日历组件提供按日期分组的事件数据
2. **周视图/月视图**：查询特定时间范围内的所有事件
3. **事件统计**：统计每天的事件数量
4. **时间规划**：查看某个时间段的事件安排

## 注意事项
1. 日期参数必须使用 `yyyy-MM-dd` 格式
2. 开始日期不能晚于结束日期
3. 返回结果包含指定范围内的所有日期，即使没有事件的日期也会返回空数组
4. 事件按开始时间的日期进行分组，跨天事件只会出现在开始日期的分组中
