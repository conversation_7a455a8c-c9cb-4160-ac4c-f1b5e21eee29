package com.barantt.familyclender.service;

import com.barantt.familyclender.dao.pojo.Families;
import com.barantt.familyclender.dao.pojo.FamilyMembers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 家庭成员服务测试类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class FamilyMembersServiceTest {

    @Autowired
    private IFamilyMembersService familyMembersService;

    @Autowired
    private IFamiliesService familiesService;

    private Integer testFamilyId;

    @BeforeEach
    public void setUp() {
        // 创建测试家庭
        Families testFamily = familiesService.createFamily("测试家庭");
        testFamilyId = testFamily.getId();
    }

    @Test
    public void testCreateFamilyMember() {
        // 测试正常创建家庭成员
        String memberName = "张三";
        FamilyMembers familyMember = familyMembersService.createFamilyMember(testFamilyId, memberName);
        
        assertNotNull(familyMember);
        assertNotNull(familyMember.getId());
        assertEquals(testFamilyId, familyMember.getFamilyId());
        assertEquals(memberName, familyMember.getName());
        assertNotNull(familyMember.getCreatedAt());
    }

    @Test
    public void testCreateFamilyMemberWithInvalidParams() {
        // 测试无效的家庭ID
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.createFamilyMember(null, "张三");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.createFamilyMember(0, "张三");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.createFamilyMember(-1, "张三");
        });

        // 测试空成员姓名
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.createFamilyMember(testFamilyId, "");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.createFamilyMember(testFamilyId, null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.createFamilyMember(testFamilyId, "   ");
        });

        // 测试不存在的家庭ID
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.createFamilyMember(99999, "张三");
        });
    }

    @Test
    public void testDeleteFamilyMember() {
        // 先创建一个家庭成员
        FamilyMembers familyMember = familyMembersService.createFamilyMember(testFamilyId, "李四");
        Integer memberId = familyMember.getId();
        
        // 测试删除
        boolean result = familyMembersService.deleteFamilyMember(memberId);
        assertTrue(result);
        
        // 验证删除后查询不到
        FamilyMembers deletedMember = familyMembersService.getFamilyMemberById(memberId);
        assertNull(deletedMember);
    }

    @Test
    public void testDeleteFamilyMemberWithInvalidParams() {
        // 测试无效的成员ID
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.deleteFamilyMember(null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.deleteFamilyMember(0);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.deleteFamilyMember(-1);
        });

        // 测试不存在的成员ID
        assertThrows(IllegalArgumentException.class, () -> {
            familyMembersService.deleteFamilyMember(99999);
        });
    }

    @Test
    public void testGetFamilyMembersByFamilyId() {
        // 创建多个家庭成员
        familyMembersService.createFamilyMember(testFamilyId, "张三");
        familyMembersService.createFamilyMember(testFamilyId, "李四");
        familyMembersService.createFamilyMember(testFamilyId, "王五");
        
        // 查询家庭成员列表
        List<FamilyMembers> familyMembers = familyMembersService.getFamilyMembersByFamilyId(testFamilyId);
        
        assertNotNull(familyMembers);
        assertEquals(3, familyMembers.size());
        
        // 验证所有成员都属于同一个家庭
        for (FamilyMembers member : familyMembers) {
            assertEquals(testFamilyId, member.getFamilyId());
        }
    }

    @Test
    public void testGetFamilyMemberById() {
        // 创建一个家庭成员
        FamilyMembers createdMember = familyMembersService.createFamilyMember(testFamilyId, "赵六");
        
        // 根据ID查询
        FamilyMembers foundMember = familyMembersService.getFamilyMemberById(createdMember.getId());
        
        assertNotNull(foundMember);
        assertEquals(createdMember.getId(), foundMember.getId());
        assertEquals(createdMember.getFamilyId(), foundMember.getFamilyId());
        assertEquals(createdMember.getName(), foundMember.getName());
    }
}
