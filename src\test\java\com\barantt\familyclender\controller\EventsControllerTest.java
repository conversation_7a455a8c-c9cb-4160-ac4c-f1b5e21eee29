package com.barantt.familyclender.controller;

import com.barantt.familyclender.dao.pojo.Events;
import com.barantt.familyclender.dao.pojo.Families;
import com.barantt.familyclender.dao.pojo.FamilyMembers;
import com.barantt.familyclender.dto.CreateEventRequest;
import com.barantt.familyclender.dto.UpdateEventRequest;
import com.barantt.familyclender.service.IEventsService;
import com.barantt.familyclender.service.IFamiliesService;
import com.barantt.familyclender.service.IFamilyMembersService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.Arrays;
import java.util.Date;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 事件控制器测试类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class EventsControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private IFamiliesService familiesService;

    @Autowired
    private IFamilyMembersService familyMembersService;

    @Autowired
    private IEventsService eventsService;

    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private Integer testFamilyId;
    private Integer testMember1Id;
    private Integer testMember2Id;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 创建测试家庭
        Families testFamily = familiesService.createFamily("测试家庭");
        testFamilyId = testFamily.getId();

        // 创建测试成员
        FamilyMembers member1 = familyMembersService.createFamilyMember(testFamilyId, "张三");
        FamilyMembers member2 = familyMembersService.createFamilyMember(testFamilyId, "李四");
        testMember1Id = member1.getId();
        testMember2Id = member2.getId();
    }

    @Test
    public void testCreateEvent() throws Exception {
        CreateEventRequest request = new CreateEventRequest();
        request.setFamilyId(testFamilyId);
        request.setTitle("家庭聚餐");
        request.setDescription("周末家庭聚餐活动");
        request.setLocation("家里");
        request.setStartTime(new Date(System.currentTimeMillis() + 86400000));
        request.setEndTime(new Date(System.currentTimeMillis() + 86400000 + 7200000));
        request.setMemberIds(Arrays.asList(testMember1Id, testMember2Id));

        mockMvc.perform(post("/events")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("事件创建成功"))
                .andExpect(jsonPath("$.data.familyId").value(testFamilyId))
                .andExpect(jsonPath("$.data.title").value("家庭聚餐"));
    }

    @Test
    public void testCreateEventWithInvalidParams() throws Exception {
        // 测试空标题
        CreateEventRequest request = new CreateEventRequest();
        request.setFamilyId(testFamilyId);
        request.setTitle("");
        request.setStartTime(new Date());
        request.setEndTime(new Date(System.currentTimeMillis() + 3600000));

        mockMvc.perform(post("/events")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));
    }

    @Test
    public void testUpdateEvent() throws Exception {
        // 先创建一个事件
        CreateEventRequest createRequest = new CreateEventRequest();
        createRequest.setFamilyId(testFamilyId);
        createRequest.setTitle("原始事件");
        createRequest.setStartTime(new Date(System.currentTimeMillis() + 86400000));
        createRequest.setEndTime(new Date(System.currentTimeMillis() + 86400000 + 3600000));

        Events event = eventsService.createEvent(createRequest);

        // 更新事件
        UpdateEventRequest updateRequest = new UpdateEventRequest();
        updateRequest.setTitle("更新后的事件");
        updateRequest.setDescription("更新后的描述");

        mockMvc.perform(put("/events/" + event.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("事件更新成功"))
                .andExpect(jsonPath("$.data.title").value("更新后的事件"));
    }

    @Test
    public void testDeleteEvent() throws Exception {
        // 先创建一个事件
        CreateEventRequest request = new CreateEventRequest();
        request.setFamilyId(testFamilyId);
        request.setTitle("待删除事件");
        request.setStartTime(new Date(System.currentTimeMillis() + 86400000));
        request.setEndTime(new Date(System.currentTimeMillis() + 86400000 + 3600000));

        Events event = eventsService.createEvent(request);

        // 删除事件
        mockMvc.perform(delete("/events/" + event.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("事件删除成功"))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void testGetEventDetail() throws Exception {
        // 创建事件
        CreateEventRequest request = new CreateEventRequest();
        request.setFamilyId(testFamilyId);
        request.setTitle("详情测试事件");
        request.setDescription("测试事件详情");
        request.setLocation("测试地点");
        request.setStartTime(new Date(System.currentTimeMillis() + 86400000));
        request.setEndTime(new Date(System.currentTimeMillis() + 86400000 + 3600000));
        request.setMemberIds(Arrays.asList(testMember1Id, testMember2Id));

        Events event = eventsService.createEvent(request);

        // 查询事件详情
        mockMvc.perform(get("/events/" + event.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.id").value(event.getId()))
                .andExpect(jsonPath("$.data.title").value("详情测试事件"))
                .andExpect(jsonPath("$.data.members").isArray())
                .andExpect(jsonPath("$.data.members.length()").value(2));
    }

    @Test
    public void testGetEventsByFamilyId() throws Exception {
        // 创建多个事件
        CreateEventRequest request1 = new CreateEventRequest();
        request1.setFamilyId(testFamilyId);
        request1.setTitle("事件1");
        request1.setStartTime(new Date(System.currentTimeMillis() + 86400000));
        request1.setEndTime(new Date(System.currentTimeMillis() + 86400000 + 3600000));

        CreateEventRequest request2 = new CreateEventRequest();
        request2.setFamilyId(testFamilyId);
        request2.setTitle("事件2");
        request2.setStartTime(new Date(System.currentTimeMillis() + 172800000));
        request2.setEndTime(new Date(System.currentTimeMillis() + 172800000 + 3600000));

        eventsService.createEvent(request1);
        eventsService.createEvent(request2);

        // 查询家庭事件列表
        mockMvc.perform(get("/events/family/" + testFamilyId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));
    }

    @Test
    public void testGetNonExistentEvent() throws Exception {
        // 查询不存在的事件
        mockMvc.perform(get("/events/99999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("事件不存在"));
    }
}
