package com.barantt.familyclender.dto;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

/**
 * 创建事件请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class CreateEventRequest {
    
    /**
     * 家庭ID
     */
    private Integer familyId;
    
    /**
     * 事件标题
     */
    private String title;
    
    /**
     * 事件描述
     */
    private String description;
    
    /**
     * 事件地点
     */
    private String location;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 参与成员ID列表
     */
    private List<Integer> memberIds;
}
