package com.barantt.familyclender.service.impl;

import com.barantt.familyclender.dao.mapper.EventMembersMapper;
import com.barantt.familyclender.dao.mapper.EventsMapper;
import com.barantt.familyclender.dao.mapper.FamiliesMapper;
import com.barantt.familyclender.dao.mapper.FamilyMembersMapper;
import com.barantt.familyclender.dao.pojo.*;
import com.barantt.familyclender.dto.CreateEventRequest;
import com.barantt.familyclender.dto.UpdateEventRequest;
import com.barantt.familyclender.service.IEventsService;
import com.barantt.familyclender.vo.EventDetailVO;
import com.barantt.familyclender.vo.EventsByDateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
@Service
public class EventsServiceImpl implements IEventsService {

    @Autowired
    private EventsMapper eventsMapper;

    @Autowired
    private EventMembersMapper eventMembersMapper;

    @Autowired
    private FamiliesMapper familiesMapper;

    @Autowired
    private FamilyMembersMapper familyMembersMapper;

    @Override
    @Transactional
    public Events createEvent(CreateEventRequest request) {
        // 参数校验
        validateCreateEventRequest(request);

        // 验证家庭是否存在
        Families family = familiesMapper.selectByPrimaryKey(request.getFamilyId());
        if (family == null) {
            throw new IllegalArgumentException("指定的家庭不存在");
        }

        // 验证参与成员是否都属于该家庭
        if (request.getMemberIds() != null && !request.getMemberIds().isEmpty()) {
            validateMembersInFamily(request.getMemberIds(), request.getFamilyId());
        }

        // 构建事件对象
        Date now = new Date();
        Events event = Events.builder()
                .familyId(request.getFamilyId())
                .title(request.getTitle().trim())
                .description(request.getDescription() != null ? request.getDescription().trim() : null)
                .location(request.getLocation() != null ? request.getLocation().trim() : null)
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .createdAt(now)
                .updatedAt(now)
                .build();

        // 保存事件
        int result = eventsMapper.insertSelective(event);
        if (result <= 0) {
            throw new RuntimeException("创建事件失败");
        }

        // 保存事件参与者
        if (request.getMemberIds() != null && !request.getMemberIds().isEmpty()) {
            saveEventMembers(event.getId(), request.getMemberIds());
        }

        return event;
    }

    @Override
    @Transactional
    public Events updateEvent(Integer eventId, UpdateEventRequest request) {
        // 参数校验
        if (eventId == null || eventId <= 0) {
            throw new IllegalArgumentException("事件ID不能为空且必须大于0");
        }
        validateUpdateEventRequest(request);

        // 验证事件是否存在
        Events existingEvent = eventsMapper.selectByPrimaryKey(eventId);
        if (existingEvent == null) {
            throw new IllegalArgumentException("指定的事件不存在");
        }

        // 验证参与成员是否都属于该家庭
        if (request.getMemberIds() != null && !request.getMemberIds().isEmpty()) {
            validateMembersInFamily(request.getMemberIds(), existingEvent.getFamilyId());
        }

        // 更新事件信息
        Date now = new Date();
        Events event = Events.builder()
                .id(eventId)
                .familyId(existingEvent.getFamilyId())
                .title(request.getTitle() != null ? request.getTitle().trim() : existingEvent.getTitle())
                .description(request.getDescription() != null ? request.getDescription().trim() : existingEvent.getDescription())
                .location(request.getLocation() != null ? request.getLocation().trim() : existingEvent.getLocation())
                .startTime(request.getStartTime() != null ? request.getStartTime() : existingEvent.getStartTime())
                .endTime(request.getEndTime() != null ? request.getEndTime() : existingEvent.getEndTime())
                .createdAt(existingEvent.getCreatedAt())
                .updatedAt(now)
                .build();

        // 更新事件
        int result = eventsMapper.updateByPrimaryKeySelective(event);
        if (result <= 0) {
            throw new RuntimeException("更新事件失败");
        }

        // 更新事件参与者
        if (request.getMemberIds() != null) {
            // 删除原有的参与者关系
            deleteEventMembersByEventId(eventId);
            // 添加新的参与者关系
            if (!request.getMemberIds().isEmpty()) {
                saveEventMembers(eventId, request.getMemberIds());
            }
        }

        return event;
    }

    @Override
    @Transactional
    public boolean deleteEvent(Integer eventId) {
        // 参数校验
        if (eventId == null || eventId <= 0) {
            throw new IllegalArgumentException("事件ID不能为空且必须大于0");
        }

        // 验证事件是否存在
        Events existingEvent = eventsMapper.selectByPrimaryKey(eventId);
        if (existingEvent == null) {
            throw new IllegalArgumentException("指定的事件不存在");
        }

        // 删除事件参与者关系
        deleteEventMembersByEventId(eventId);

        // 删除事件
        int result = eventsMapper.deleteByPrimaryKey(eventId);
        return result > 0;
    }

    @Override
    public EventDetailVO getEventDetail(Integer eventId) {
        // 参数校验
        if (eventId == null || eventId <= 0) {
            throw new IllegalArgumentException("事件ID不能为空且必须大于0");
        }

        // 查询事件基本信息
        Events event = eventsMapper.selectByPrimaryKey(eventId);
        if (event == null) {
            return null;
        }

        // 查询参与成员
        List<FamilyMembers> members = getEventMembers(eventId);

        // 构建详情VO
        EventDetailVO eventDetail = new EventDetailVO();
        eventDetail.setId(event.getId());
        eventDetail.setFamilyId(event.getFamilyId());
        eventDetail.setTitle(event.getTitle());
        eventDetail.setDescription(event.getDescription());
        eventDetail.setLocation(event.getLocation());
        eventDetail.setStartTime(event.getStartTime());
        eventDetail.setEndTime(event.getEndTime());
        eventDetail.setCreatedAt(event.getCreatedAt());
        eventDetail.setUpdatedAt(event.getUpdatedAt());
        eventDetail.setMembers(members);

        return eventDetail;
    }

    @Override
    public List<Events> getEventsByFamilyId(Integer familyId) {
        // 参数校验
        if (familyId == null || familyId <= 0) {
            throw new IllegalArgumentException("家庭ID不能为空且必须大于0");
        }

        // 构建查询条件
        Example example = new Example(Events.class);
        example.createCriteria().andEqualTo("familyId", familyId);
        example.orderBy("startTime").asc();

        return eventsMapper.selectByExample(example);
    }

    @Override
    public Events getEventById(Integer eventId) {
        // 参数校验
        if (eventId == null || eventId <= 0) {
            throw new IllegalArgumentException("事件ID不能为空且必须大于0");
        }

        return eventsMapper.selectByPrimaryKey(eventId);
    }

    /**
     * 验证创建事件请求参数
     */
    private void validateCreateEventRequest(CreateEventRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getFamilyId() == null || request.getFamilyId() <= 0) {
            throw new IllegalArgumentException("家庭ID不能为空且必须大于0");
        }
        if (!StringUtils.hasText(request.getTitle())) {
            throw new IllegalArgumentException("事件标题不能为空");
        }
        if (request.getStartTime() == null) {
            throw new IllegalArgumentException("开始时间不能为空");
        }
        if (request.getEndTime() == null) {
            throw new IllegalArgumentException("结束时间不能为空");
        }
        if (request.getStartTime().after(request.getEndTime())) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
    }

    /**
     * 验证更新事件请求参数
     */
    private void validateUpdateEventRequest(UpdateEventRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getStartTime() != null && request.getEndTime() != null) {
            if (request.getStartTime().after(request.getEndTime())) {
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }
        }
    }

    /**
     * 验证成员是否都属于指定家庭
     */
    private void validateMembersInFamily(List<Integer> memberIds, Integer familyId) {
        for (Integer memberId : memberIds) {
            FamilyMembers member = familyMembersMapper.selectByPrimaryKey(memberId);
            if (member == null) {
                throw new IllegalArgumentException("成员ID " + memberId + " 不存在");
            }
            if (!member.getFamilyId().equals(familyId)) {
                throw new IllegalArgumentException("成员ID " + memberId + " 不属于指定家庭");
            }
        }
    }

    /**
     * 保存事件参与者
     */
    private void saveEventMembers(Integer eventId, List<Integer> memberIds) {
        Date now = new Date();
        for (Integer memberId : memberIds) {
            EventMembers eventMember = EventMembers.builder()
                    .eventId(eventId)
                    .memberId(memberId)
                    .createdAt(now)
                    .build();
            eventMembersMapper.insertSelective(eventMember);
        }
    }

    /**
     * 删除事件的所有参与者
     */
    private void deleteEventMembersByEventId(Integer eventId) {
        Example example = new Example(EventMembers.class);
        example.createCriteria().andEqualTo("eventId", eventId);
        eventMembersMapper.deleteByExample(example);
    }

    /**
     * 查询事件的参与成员
     */
    private List<FamilyMembers> getEventMembers(Integer eventId) {
        // 查询事件参与者关系
        Example example = new Example(EventMembers.class);
        example.createCriteria().andEqualTo("eventId", eventId);
        List<EventMembers> eventMembers = eventMembersMapper.selectByExample(example);

        // 查询成员详细信息
        List<FamilyMembers> members = new ArrayList<>();
        for (EventMembers eventMember : eventMembers) {
            FamilyMembers member = familyMembersMapper.selectByPrimaryKey(eventMember.getMemberId());
            if (member != null) {
                members.add(member);
            }
        }

        return members;
    }

    @Override
    public List<EventsByDateVO> getEventsByDateRange(Integer familyId, Date startDate, Date endDate) {
        // 参数校验
        if (familyId == null || familyId <= 0) {
            throw new IllegalArgumentException("家庭ID不能为空且必须大于0");
        }
        if (startDate == null) {
            throw new IllegalArgumentException("开始日期不能为空");
        }
        if (endDate == null) {
            throw new IllegalArgumentException("结束日期不能为空");
        }
        if (startDate.after(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        // 查询指定时间范围内的事件
        List<Events> events = eventsMapper.selectByFamilyIdAndDateRange(familyId, startDate, endDate);

        // 按日期分组
        Map<String, List<Events>> eventsByDate = events.stream()
                .collect(Collectors.groupingBy(event -> {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    return sdf.format(event.getStartTime());
                }));

        // 构建返回结果
        List<EventsByDateVO> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 生成日期范围内的所有日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        while (!calendar.getTime().after(endDate)) {
            String dateStr = sdf.format(calendar.getTime());
            List<Events> dayEvents = eventsByDate.getOrDefault(dateStr, new ArrayList<>());

            EventsByDateVO dayEventVO = new EventsByDateVO();
            dayEventVO.setDate(calendar.getTime());
            dayEventVO.setEvents(dayEvents);
            dayEventVO.setEventCount(dayEvents.size());

            result.add(dayEventVO);

            // 移动到下一天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        return result;
    }
}
