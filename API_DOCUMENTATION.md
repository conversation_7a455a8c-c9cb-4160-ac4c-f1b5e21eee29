# 家庭日历系统 API 文档

## 新增家庭接口

### 接口描述
创建一个新的家庭

### 请求信息
- **URL**: `/families`
- **Method**: `POST`
- **Content-Type**: `application/json`

### 请求参数
```json
{
    "name": "家庭名称"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| name | String | 是 | 家庭名称，不能为空 |

### 响应格式
```json
{
    "code": 200,
    "message": "家庭创建成功",
    "data": {
        "id": 1,
        "name": "张三家庭",
        "createdAt": "2025-07-02T10:30:00",
        "updatedAt": "2025-07-02T10:30:00"
    }
}
```

### 响应参数说明
| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.id | Integer | 家庭ID |
| data.name | String | 家庭名称 |
| data.createdAt | Date | 创建时间 |
| data.updatedAt | Date | 更新时间 |

### 错误响应
#### 参数错误
```json
{
    "code": 400,
    "message": "家庭名称不能为空",
    "data": null
}
```

#### 系统错误
```json
{
    "code": 500,
    "message": "创建家庭失败：数据库连接异常",
    "data": null
}
```

### 示例

#### 请求示例
```bash
curl -X POST http://localhost:8080/families \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三家庭"
  }'
```

#### 成功响应示例
```json
{
    "code": 200,
    "message": "家庭创建成功",
    "data": {
        "id": 1,
        "name": "张三家庭",
        "createdAt": "2025-07-02T10:30:00.000+00:00",
        "updatedAt": "2025-07-02T10:30:00.000+00:00"
    }
}
```

### 状态码说明
| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |
