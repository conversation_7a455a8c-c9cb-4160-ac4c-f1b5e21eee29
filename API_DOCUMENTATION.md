# 家庭日历系统 API 文档

## 新增家庭接口

### 接口描述
创建一个新的家庭

### 请求信息
- **URL**: `/families`
- **Method**: `POST`
- **Content-Type**: `application/json`

### 请求参数
```json
{
    "name": "家庭名称"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| name | String | 是 | 家庭名称，不能为空 |

### 响应格式
```json
{
    "code": 200,
    "message": "家庭创建成功",
    "data": {
        "id": 1,
        "name": "张三家庭",
        "createdAt": "2025-07-02T10:30:00",
        "updatedAt": "2025-07-02T10:30:00"
    }
}
```

### 响应参数说明
| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.id | Integer | 家庭ID |
| data.name | String | 家庭名称 |
| data.createdAt | Date | 创建时间 |
| data.updatedAt | Date | 更新时间 |

### 错误响应
#### 参数错误
```json
{
    "code": 400,
    "message": "家庭名称不能为空",
    "data": null
}
```

#### 系统错误
```json
{
    "code": 500,
    "message": "创建家庭失败：数据库连接异常",
    "data": null
}
```

### 示例

#### 请求示例
```bash
curl -X POST http://localhost:8080/families \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三家庭"
  }'
```

#### 成功响应示例
```json
{
    "code": 200,
    "message": "家庭创建成功",
    "data": {
        "id": 1,
        "name": "张三家庭",
        "createdAt": "2025-07-02T10:30:00.000+00:00",
        "updatedAt": "2025-07-02T10:30:00.000+00:00"
    }
}
```

### 状态码说明
| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 家庭成员管理接口

### 新增家庭成员

#### 接口描述
为指定家庭添加新成员

#### 请求信息
- **URL**: `/family-members`
- **Method**: `POST`
- **Content-Type**: `application/json`

#### 请求参数
```json
{
    "familyId": 1,
    "name": "张三"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| familyId | Integer | 是 | 家庭ID，必须大于0 |
| name | String | 是 | 成员姓名，不能为空 |

#### 响应格式
```json
{
    "code": 200,
    "message": "家庭成员创建成功",
    "data": {
        "id": 1,
        "familyId": 1,
        "name": "张三",
        "createdAt": "2025-07-02T10:30:00"
    }
}
```

#### 请求示例
```bash
curl -X POST http://localhost:8080/family-members \
  -H "Content-Type: application/json" \
  -d '{
    "familyId": 1,
    "name": "张三"
  }'
```

### 删除家庭成员

#### 接口描述
删除指定的家庭成员

#### 请求信息
- **URL**: `/family-members/{memberId}`
- **Method**: `DELETE`

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| memberId | Integer | 是 | 成员ID，必须大于0 |

#### 响应格式
```json
{
    "code": 200,
    "message": "家庭成员删除成功",
    "data": true
}
```

#### 请求示例
```bash
curl -X DELETE http://localhost:8080/family-members/1
```

### 查询家庭成员列表

#### 接口描述
根据家庭ID查询该家庭的所有成员

#### 请求信息
- **URL**: `/family-members/family/{familyId}`
- **Method**: `GET`

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| familyId | Integer | 是 | 家庭ID，必须大于0 |

#### 响应格式
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "id": 1,
            "familyId": 1,
            "name": "张三",
            "createdAt": "2025-07-02T10:30:00"
        },
        {
            "id": 2,
            "familyId": 1,
            "name": "李四",
            "createdAt": "2025-07-02T10:31:00"
        }
    ]
}
```

#### 请求示例
```bash
curl -X GET http://localhost:8080/family-members/family/1
```

### 查询单个家庭成员

#### 接口描述
根据成员ID查询家庭成员详细信息

#### 请求信息
- **URL**: `/family-members/{memberId}`
- **Method**: `GET`

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| memberId | Integer | 是 | 成员ID，必须大于0 |

#### 响应格式
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "id": 1,
        "familyId": 1,
        "name": "张三",
        "createdAt": "2025-07-02T10:30:00"
    }
}
```

#### 请求示例
```bash
curl -X GET http://localhost:8080/family-members/1
```
