package com.barantt.familyclender.service;

import com.barantt.familyclender.dao.pojo.FamilyMembers;

import java.util.List;

/**
 *  服务类接口
 *
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
public interface IFamilyMembersService {

    /**
     * 新增家庭成员
     *
     * @param familyId 家庭ID
     * @param memberName 成员姓名
     * @return 新增的家庭成员信息
     */
    FamilyMembers createFamilyMember(Integer familyId, String memberName);

    /**
     * 删除家庭成员
     *
     * @param memberId 成员ID
     * @return 是否删除成功
     */
    boolean deleteFamilyMember(Integer memberId);

    /**
     * 根据家庭ID查询家庭成员列表
     *
     * @param familyId 家庭ID
     * @return 家庭成员列表
     */
    List<FamilyMembers> getFamilyMembersByFamilyId(Integer familyId);

    /**
     * 根据成员ID查询家庭成员
     *
     * @param memberId 成员ID
     * @return 家庭成员信息
     */
    FamilyMembers getFamilyMemberById(Integer memberId);
}
