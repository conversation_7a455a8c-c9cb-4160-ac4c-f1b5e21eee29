# 应用服务 WEB 访问端口
server.port=8080

server.servlet.context-path=/api

# 数据库配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=**********************************************************************************************************************************************************
spring.datasource.username=barantt
spring.datasource.password=cxlLLF1314

# MyBatis配置
mybatis.mapper-locations=classpath:mapper/*xml
mybatis.type-aliases-package=com.barantt.familyclender.dao.pojo

# 日志配置
logging.level.com.barantt.familyclender.dao.mapper=DEBUG

