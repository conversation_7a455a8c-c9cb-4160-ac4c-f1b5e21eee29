package com.barantt.familyclender.service.impl;

import com.barantt.familyclender.dao.mapper.FamiliesMapper;
import com.barantt.familyclender.dao.mapper.FamilyMembersMapper;
import com.barantt.familyclender.dao.pojo.Families;
import com.barantt.familyclender.dao.pojo.FamilyMembers;
import com.barantt.familyclender.service.IFamilyMembersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
@Service
public class FamilyMembersServiceImpl implements IFamilyMembersService {

    @Autowired
    private FamilyMembersMapper familyMembersMapper;

    @Autowired
    private FamiliesMapper familiesMapper;

    @Override
    public FamilyMembers createFamilyMember(Integer familyId, String memberName) {
        // 参数校验
        if (familyId == null || familyId <= 0) {
            throw new IllegalArgumentException("家庭ID不能为空且必须大于0");
        }
        if (!StringUtils.hasText(memberName)) {
            throw new IllegalArgumentException("成员姓名不能为空");
        }

        // 验证家庭是否存在
        Families family = familiesMapper.selectByPrimaryKey(familyId);
        if (family == null) {
            throw new IllegalArgumentException("指定的家庭不存在");
        }

        // 构建家庭成员对象
        Date now = new Date();
        FamilyMembers familyMember = FamilyMembers.builder()
                .familyId(familyId)
                .name(memberName.trim())
                .createdAt(now)
                .build();

        // 保存到数据库
        int result = familyMembersMapper.insertSelective(familyMember);
        if (result <= 0) {
            throw new RuntimeException("创建家庭成员失败");
        }

        return familyMember;
    }

    @Override
    public boolean deleteFamilyMember(Integer memberId) {
        // 参数校验
        if (memberId == null || memberId <= 0) {
            throw new IllegalArgumentException("成员ID不能为空且必须大于0");
        }

        // 验证成员是否存在
        FamilyMembers existingMember = familyMembersMapper.selectByPrimaryKey(memberId);
        if (existingMember == null) {
            throw new IllegalArgumentException("指定的家庭成员不存在");
        }

        // 删除成员
        int result = familyMembersMapper.deleteByPrimaryKey(memberId);
        return result > 0;
    }

    @Override
    public List<FamilyMembers> getFamilyMembersByFamilyId(Integer familyId) {
        // 参数校验
        if (familyId == null || familyId <= 0) {
            throw new IllegalArgumentException("家庭ID不能为空且必须大于0");
        }

        // 构建查询条件
        Example example = new Example(FamilyMembers.class);
        example.createCriteria().andEqualTo("familyId", familyId);
        example.orderBy("createdAt").asc();

        return familyMembersMapper.selectByExample(example);
    }

    @Override
    public FamilyMembers getFamilyMemberById(Integer memberId) {
        // 参数校验
        if (memberId == null || memberId <= 0) {
            throw new IllegalArgumentException("成员ID不能为空且必须大于0");
        }

        return familyMembersMapper.selectByPrimaryKey(memberId);
    }
}
