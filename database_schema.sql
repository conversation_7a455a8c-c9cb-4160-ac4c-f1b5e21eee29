-- 家庭日历系统数据库建表脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS familyclender DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE familyclender;

-- 创建families表（家庭表）
CREATE TABLE families (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '家庭ID',
    name VARCHAR(255) NOT NULL COMMENT '家庭名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家庭表';

-- 创建family_members表（家庭成员表）
CREATE TABLE family_members (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '成员ID',
    family_id INT NOT NULL COMMENT '家庭ID',
    name VARCHAR(255) NOT NULL COMMENT '成员姓名',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_family_id (family_id),
    FOREIGN KEY (family_id) REFERENCES families(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家庭成员表';

-- 创建events表（事件表）
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '事件ID',
    family_id INT NOT NULL COMMENT '家庭ID',
    title VARCHAR(255) NOT NULL COMMENT '事件标题',
    description TEXT COMMENT '事件描述',
    location VARCHAR(255) COMMENT '事件地点',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_family_id (family_id),
    INDEX idx_start_time (start_time),
    FOREIGN KEY (family_id) REFERENCES families(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事件表';

-- 创建event_members表（事件参与者表）
CREATE TABLE event_members (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    event_id INT NOT NULL COMMENT '事件ID',
    member_id INT NOT NULL COMMENT '成员ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_event_id (event_id),
    INDEX idx_member_id (member_id),
    UNIQUE KEY uk_event_member (event_id, member_id),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES family_members(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事件参与者表';

-- 插入示例数据
INSERT INTO families (name) VALUES ('张三家庭');
INSERT INTO families (name) VALUES ('李四家庭');

INSERT INTO family_members (family_id, name) VALUES (1, '张三');
INSERT INTO family_members (family_id, name) VALUES (1, '张三妻子');
INSERT INTO family_members (family_id, name) VALUES (1, '张三儿子');

INSERT INTO family_members (family_id, name) VALUES (2, '李四');
INSERT INTO family_members (family_id, name) VALUES (2, '李四妻子');
