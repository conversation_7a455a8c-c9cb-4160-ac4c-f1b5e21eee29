package com.barantt.familyclender.service;

import com.barantt.familyclender.dao.pojo.Events;
import com.barantt.familyclender.dto.CreateEventRequest;
import com.barantt.familyclender.dto.UpdateEventRequest;
import com.barantt.familyclender.vo.EventDetailVO;
import com.barantt.familyclender.vo.EventsByDateVO;

import java.util.Date;
import java.util.List;

/**
 *  服务类接口
 *
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
public interface IEventsService {

    /**
     * 创建事件
     *
     * @param request 创建事件请求
     * @return 创建的事件信息
     */
    Events createEvent(CreateEventRequest request);

    /**
     * 更新事件
     *
     * @param eventId 事件ID
     * @param request 更新事件请求
     * @return 更新后的事件信息
     */
    Events updateEvent(Integer eventId, UpdateEventRequest request);

    /**
     * 删除事件
     *
     * @param eventId 事件ID
     * @return 是否删除成功
     */
    boolean deleteEvent(Integer eventId);

    /**
     * 根据事件ID查询事件详情
     *
     * @param eventId 事件ID
     * @return 事件详情
     */
    EventDetailVO getEventDetail(Integer eventId);

    /**
     * 根据家庭ID查询事件列表
     *
     * @param familyId 家庭ID
     * @return 事件列表
     */
    List<Events> getEventsByFamilyId(Integer familyId);

    /**
     * 根据事件ID查询事件基本信息
     *
     * @param eventId 事件ID
     * @return 事件信息
     */
    Events getEventById(Integer eventId);

    /**
     * 根据家庭ID和时间范围查询事件，按日期分组
     *
     * @param familyId 家庭ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按日期分组的事件列表
     */
    List<EventsByDateVO> getEventsByDateRange(Integer familyId, Date startDate, Date endDate);
}
