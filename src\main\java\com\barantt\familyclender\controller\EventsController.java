package com.barantt.familyclender.controller;

import com.barantt.familyclender.common.Result;
import com.barantt.familyclender.dao.pojo.Events;
import com.barantt.familyclender.dto.CreateEventRequest;
import com.barantt.familyclender.dto.UpdateEventRequest;
import com.barantt.familyclender.service.IEventsService;
import com.barantt.familyclender.vo.EventDetailVO;
import com.barantt.familyclender.vo.EventsByDateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 *  前端控制器
 *
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
@RestController
@RequestMapping("/events")
public class EventsController {

    @Autowired
    private IEventsService eventsService;

    /**
     * 创建事件
     *
     * @param request 创建事件请求
     * @return 创建结果
     */
    @PostMapping
    public Result<Events> createEvent(@RequestBody CreateEventRequest request) {
        try {
            Events event = eventsService.createEvent(request);
            return Result.success("事件创建成功", event);
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("创建事件失败：" + e.getMessage());
        }
    }

    /**
     * 更新事件
     *
     * @param eventId 事件ID
     * @param request 更新事件请求
     * @return 更新结果
     */
    @PutMapping("/{eventId}")
    public Result<Events> updateEvent(@PathVariable Integer eventId, @RequestBody UpdateEventRequest request) {
        try {
            Events event = eventsService.updateEvent(eventId, request);
            return Result.success("事件更新成功", event);
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("更新事件失败：" + e.getMessage());
        }
    }

    /**
     * 删除事件
     *
     * @param eventId 事件ID
     * @return 删除结果
     */
    @DeleteMapping("/{eventId}")
    public Result<Boolean> deleteEvent(@PathVariable Integer eventId) {
        try {
            boolean success = eventsService.deleteEvent(eventId);
            if (success) {
                return Result.success("事件删除成功", true);
            } else {
                return Result.error("删除事件失败");
            }
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("删除事件失败：" + e.getMessage());
        }
    }

    /**
     * 查询事件详情
     *
     * @param eventId 事件ID
     * @return 事件详情
     */
    @GetMapping("/{eventId}")
    public Result<EventDetailVO> getEventDetail(@PathVariable Integer eventId) {
        try {
            EventDetailVO eventDetail = eventsService.getEventDetail(eventId);
            if (eventDetail != null) {
                return Result.success("查询成功", eventDetail);
            } else {
                return Result.error(404, "事件不存在");
            }
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("查询事件失败：" + e.getMessage());
        }
    }

    /**
     * 根据家庭ID查询事件列表
     *
     * @param familyId 家庭ID
     * @return 事件列表
     */
    @GetMapping("/family/{familyId}")
    public Result<List<Events>> getEventsByFamilyId(@PathVariable Integer familyId) {
        try {
            List<Events> events = eventsService.getEventsByFamilyId(familyId);
            return Result.success("查询成功", events);
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("查询事件列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据家庭ID和时间范围查询事件，按日期分组
     *
     * @param familyId 家庭ID
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 按日期分组的事件列表
     */
    @GetMapping("/family/{familyId}/date-range")
    public Result<List<EventsByDateVO>> getEventsByDateRange(
            @PathVariable Integer familyId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            List<EventsByDateVO> events = eventsService.getEventsByDateRange(familyId, startDate, endDate);
            return Result.success("查询成功", events);
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("查询事件列表失败：" + e.getMessage());
        }
    }
}
