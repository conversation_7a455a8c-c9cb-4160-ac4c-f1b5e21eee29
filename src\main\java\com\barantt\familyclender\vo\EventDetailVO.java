package com.barantt.familyclender.vo;

import com.barantt.familyclender.dao.pojo.FamilyMembers;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

/**
 * 事件详情VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class EventDetailVO {
    
    /**
     * 事件ID
     */
    private Integer id;
    
    /**
     * 家庭ID
     */
    private Integer familyId;
    
    /**
     * 事件标题
     */
    private String title;
    
    /**
     * 事件描述
     */
    private String description;
    
    /**
     * 事件地点
     */
    private String location;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
    
    /**
     * 参与成员列表
     */
    private List<FamilyMembers> members;
}
