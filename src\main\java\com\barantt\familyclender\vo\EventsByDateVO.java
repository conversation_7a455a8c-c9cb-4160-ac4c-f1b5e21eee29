package com.barantt.familyclender.vo;

import com.barantt.familyclender.dao.pojo.Events;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

/**
 * 按日期分组的事件VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class EventsByDateVO {
    
    /**
     * 日期（年-月-日）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;
    
    /**
     * 该日期的事件列表
     */
    private List<Events> events;
    
    /**
     * 该日期的事件数量
     */
    private Integer eventCount;
}
