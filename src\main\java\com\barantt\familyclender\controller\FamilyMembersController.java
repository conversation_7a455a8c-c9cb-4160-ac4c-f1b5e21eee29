package com.barantt.familyclender.controller;

import com.barantt.familyclender.common.Result;
import com.barantt.familyclender.dao.pojo.FamilyMembers;
import com.barantt.familyclender.dto.CreateFamilyMemberRequest;
import com.barantt.familyclender.service.IFamilyMembersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  前端控制器
 *
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
@RestController
@RequestMapping("/family-members")
public class FamilyMembersController {

    @Autowired
    private IFamilyMembersService familyMembersService;

    /**
     * 新增家庭成员
     *
     * @param request 创建家庭成员请求
     * @return 创建结果
     */
    @PostMapping
    public Result<FamilyMembers> createFamilyMember(@RequestBody CreateFamilyMemberRequest request) {
        try {
            FamilyMembers familyMember = familyMembersService.createFamilyMember(
                    request.getFamilyId(),
                    request.getName()
            );
            return Result.success("家庭成员创建成功", familyMember);
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("创建家庭成员失败：" + e.getMessage());
        }
    }

    /**
     * 删除家庭成员
     *
     * @param memberId 成员ID
     * @return 删除结果
     */
    @DeleteMapping("/{memberId}")
    public Result<Boolean> deleteFamilyMember(@PathVariable Integer memberId) {
        try {
            boolean success = familyMembersService.deleteFamilyMember(memberId);
            if (success) {
                return Result.success("家庭成员删除成功", true);
            } else {
                return Result.error("删除家庭成员失败");
            }
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("删除家庭成员失败：" + e.getMessage());
        }
    }

    /**
     * 根据家庭ID查询家庭成员列表
     *
     * @param familyId 家庭ID
     * @return 家庭成员列表
     */
    @GetMapping("/family/{familyId}")
    public Result<List<FamilyMembers>> getFamilyMembersByFamilyId(@PathVariable Integer familyId) {
        try {
            List<FamilyMembers> familyMembers = familyMembersService.getFamilyMembersByFamilyId(familyId);
            return Result.success("查询成功", familyMembers);
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("查询家庭成员失败：" + e.getMessage());
        }
    }

    /**
     * 根据成员ID查询家庭成员
     *
     * @param memberId 成员ID
     * @return 家庭成员信息
     */
    @GetMapping("/{memberId}")
    public Result<FamilyMembers> getFamilyMemberById(@PathVariable Integer memberId) {
        try {
            FamilyMembers familyMember = familyMembersService.getFamilyMemberById(memberId);
            if (familyMember != null) {
                return Result.success("查询成功", familyMember);
            } else {
                return Result.error(404, "家庭成员不存在");
            }
        } catch (IllegalArgumentException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("查询家庭成员失败：" + e.getMessage());
        }
    }
}
