#!/bin/bash

# 按日期范围查询事件功能演示脚本
# 使用方法: ./demo_events_by_date.sh

BASE_URL="http://localhost:8080/api"

echo "=== 按日期范围查询事件功能演示 ==="
echo

# 1. 创建演示家庭
echo "1. 创建演示家庭..."
FAMILY_RESPONSE=$(curl -s -X POST $BASE_URL/families \
  -H "Content-Type: application/json" \
  -d '{
    "name": "演示家庭"
  }')

echo "响应: $FAMILY_RESPONSE"
FAMILY_ID=$(echo $FAMILY_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "创建的家庭ID: $FAMILY_ID"
echo

# 2. 创建家庭成员
echo "2. 创建家庭成员..."

echo "2.1 创建爸爸"
DAD_RESPONSE=$(curl -s -X POST $BASE_URL/family-members \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"name\": \"爸爸\"
  }")
DAD_ID=$(echo $DAD_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "爸爸ID: $DAD_ID"

echo "2.2 创建妈妈"
MOM_RESPONSE=$(curl -s -X POST $BASE_URL/family-members \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"name\": \"妈妈\"
  }")
MOM_ID=$(echo $MOM_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "妈妈ID: $MOM_ID"

echo "2.3 创建孩子"
CHILD_RESPONSE=$(curl -s -X POST $BASE_URL/family-members \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"name\": \"孩子\"
  }")
CHILD_ID=$(echo $CHILD_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "孩子ID: $CHILD_ID"
echo

# 3. 创建一周的事件安排
echo "3. 创建一周的事件安排..."

echo "3.1 周一 - 工作日早餐"
MON_BREAKFAST=$(curl -s -X POST $BASE_URL/events \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"title\": \"工作日早餐\",
    \"description\": \"全家一起吃早餐\",
    \"location\": \"家里餐厅\",
    \"startTime\": \"2025-07-07 07:00:00\",
    \"endTime\": \"2025-07-07 07:30:00\",
    \"memberIds\": [$DAD_ID, $MOM_ID, $CHILD_ID]
  }")
echo "周一早餐事件创建完成"

echo "3.2 周一 - 爸爸加班"
MON_OVERTIME=$(curl -s -X POST $BASE_URL/events \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"title\": \"爸爸加班\",
    \"description\": \"项目紧急，需要加班\",
    \"location\": \"公司\",
    \"startTime\": \"2025-07-07 19:00:00\",
    \"endTime\": \"2025-07-07 22:00:00\",
    \"memberIds\": [$DAD_ID]
  }")
echo "周一加班事件创建完成"

echo "3.3 周二 - 妈妈瑜伽课"
TUE_YOGA=$(curl -s -X POST $BASE_URL/events \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"title\": \"瑜伽课\",
    \"description\": \"妈妈的瑜伽课程\",
    \"location\": \"健身房\",
    \"startTime\": \"2025-07-08 19:00:00\",
    \"endTime\": \"2025-07-08 20:30:00\",
    \"memberIds\": [$MOM_ID]
  }")
echo "周二瑜伽事件创建完成"

echo "3.4 周三 - 孩子钢琴课"
WED_PIANO=$(curl -s -X POST $BASE_URL/events \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"title\": \"钢琴课\",
    \"description\": \"孩子的钢琴课程\",
    \"location\": \"音乐学校\",
    \"startTime\": \"2025-07-09 16:00:00\",
    \"endTime\": \"2025-07-09 17:00:00\",
    \"memberIds\": [$CHILD_ID, $MOM_ID]
  }")
echo "周三钢琴课事件创建完成"

echo "3.5 周五 - 家庭聚餐"
FRI_DINNER=$(curl -s -X POST $BASE_URL/events \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"title\": \"周末家庭聚餐\",
    \"description\": \"庆祝一周工作结束\",
    \"location\": \"家里餐厅\",
    \"startTime\": \"2025-07-11 18:00:00\",
    \"endTime\": \"2025-07-11 20:00:00\",
    \"memberIds\": [$DAD_ID, $MOM_ID, $CHILD_ID]
  }")
echo "周五聚餐事件创建完成"

echo "3.6 周六 - 全家购物"
SAT_SHOPPING=$(curl -s -X POST $BASE_URL/events \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"title\": \"全家购物\",
    \"description\": \"周末购买生活用品\",
    \"location\": \"购物中心\",
    \"startTime\": \"2025-07-12 10:00:00\",
    \"endTime\": \"2025-07-12 12:00:00\",
    \"memberIds\": [$DAD_ID, $MOM_ID, $CHILD_ID]
  }")

SAT_MOVIE=$(curl -s -X POST $BASE_URL/events \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"title\": \"看电影\",
    \"description\": \"全家一起看电影\",
    \"location\": \"电影院\",
    \"startTime\": \"2025-07-12 15:00:00\",
    \"endTime\": \"2025-07-12 17:00:00\",
    \"memberIds\": [$DAD_ID, $MOM_ID, $CHILD_ID]
  }")
echo "周六购物和看电影事件创建完成"

echo "3.7 周日 - 休息日"
SUN_REST=$(curl -s -X POST $BASE_URL/events \
  -H "Content-Type: application/json" \
  -d "{
    \"familyId\": $FAMILY_ID,
    \"title\": \"家庭休息日\",
    \"description\": \"在家休息，享受家庭时光\",
    \"location\": \"家里\",
    \"startTime\": \"2025-07-13 09:00:00\",
    \"endTime\": \"2025-07-13 18:00:00\",
    \"memberIds\": [$DAD_ID, $MOM_ID, $CHILD_ID]
  }")
echo "周日休息事件创建完成"
echo

# 4. 按日期范围查询事件
echo "4. 按日期范围查询事件..."

echo "4.1 查询整周事件（2025-07-07 到 2025-07-13）"
WEEK_EVENTS=$(curl -s -X GET "$BASE_URL/events/family/$FAMILY_ID/date-range?startDate=2025-07-07&endDate=2025-07-13")
echo "整周事件: $WEEK_EVENTS"
echo

echo "4.2 查询工作日事件（2025-07-07 到 2025-07-11）"
WEEKDAY_EVENTS=$(curl -s -X GET "$BASE_URL/events/family/$FAMILY_ID/date-range?startDate=2025-07-07&endDate=2025-07-11")
echo "工作日事件: $WEEKDAY_EVENTS"
echo

echo "4.3 查询周末事件（2025-07-12 到 2025-07-13）"
WEEKEND_EVENTS=$(curl -s -X GET "$BASE_URL/events/family/$FAMILY_ID/date-range?startDate=2025-07-12&endDate=2025-07-13")
echo "周末事件: $WEEKEND_EVENTS"
echo

echo "4.4 查询单天事件（2025-07-12）"
SINGLE_DAY_EVENTS=$(curl -s -X GET "$BASE_URL/events/family/$FAMILY_ID/date-range?startDate=2025-07-12&endDate=2025-07-12")
echo "单天事件: $SINGLE_DAY_EVENTS"
echo

echo "4.5 查询没有事件的日期范围（2025-07-20 到 2025-07-22）"
EMPTY_EVENTS=$(curl -s -X GET "$BASE_URL/events/family/$FAMILY_ID/date-range?startDate=2025-07-20&endDate=2025-07-22")
echo "空日期范围事件: $EMPTY_EVENTS"
echo

# 5. 测试错误情况
echo "5. 测试错误情况..."

echo "5.1 测试无效的日期格式"
INVALID_DATE=$(curl -s -X GET "$BASE_URL/events/family/$FAMILY_ID/date-range?startDate=invalid-date&endDate=2025-07-13")
echo "无效日期格式响应: $INVALID_DATE"

echo "5.2 测试开始日期晚于结束日期"
INVALID_RANGE=$(curl -s -X GET "$BASE_URL/events/family/$FAMILY_ID/date-range?startDate=2025-07-13&endDate=2025-07-07")
echo "无效日期范围响应: $INVALID_RANGE"

echo "5.3 测试不存在的家庭ID"
INVALID_FAMILY=$(curl -s -X GET "$BASE_URL/events/family/99999/date-range?startDate=2025-07-07&endDate=2025-07-13")
echo "无效家庭ID响应: $INVALID_FAMILY"
echo

echo "=== 按日期范围查询事件功能演示完成 ==="
echo
echo "演示总结："
echo "- 创建了1个家庭和3个家庭成员"
echo "- 创建了一周的事件安排（7天，共9个事件）"
echo "- 演示了按不同日期范围查询事件的功能"
echo "- 展示了按日期分组的返回结果"
echo "- 测试了各种错误情况的处理"
