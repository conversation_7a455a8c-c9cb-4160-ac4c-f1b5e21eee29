package com.barantt.familyclender.controller;

import com.barantt.familyclender.dao.pojo.Families;
import com.barantt.familyclender.dao.pojo.FamilyMembers;
import com.barantt.familyclender.dto.CreateEventRequest;
import com.barantt.familyclender.service.IEventsService;
import com.barantt.familyclender.service.IFamiliesService;
import com.barantt.familyclender.service.IFamilyMembersService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.text.SimpleDateFormat;
import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 按日期范围查询事件控制器测试类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class EventsByDateRangeControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private IFamiliesService familiesService;

    @Autowired
    private IFamilyMembersService familyMembersService;

    @Autowired
    private IEventsService eventsService;

    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private Integer testFamilyId;
    private Integer testMember1Id;
    private Integer testMember2Id;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 创建测试家庭
        Families testFamily = familiesService.createFamily("测试家庭");
        testFamilyId = testFamily.getId();

        // 创建测试成员
        FamilyMembers member1 = familyMembersService.createFamilyMember(testFamilyId, "张三");
        FamilyMembers member2 = familyMembersService.createFamilyMember(testFamilyId, "李四");
        testMember1Id = member1.getId();
        testMember2Id = member2.getId();
    }

    @Test
    public void testGetEventsByDateRange() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 创建不同日期的事件
        CreateEventRequest request1 = new CreateEventRequest();
        request1.setFamilyId(testFamilyId);
        request1.setTitle("第一天事件1");
        request1.setStartTime(sdf.parse("2025-07-05 09:00:00"));
        request1.setEndTime(sdf.parse("2025-07-05 10:00:00"));
        request1.setMemberIds(Arrays.asList(testMember1Id));

        CreateEventRequest request2 = new CreateEventRequest();
        request2.setFamilyId(testFamilyId);
        request2.setTitle("第一天事件2");
        request2.setStartTime(sdf.parse("2025-07-05 14:00:00"));
        request2.setEndTime(sdf.parse("2025-07-05 15:00:00"));
        request2.setMemberIds(Arrays.asList(testMember2Id));

        CreateEventRequest request3 = new CreateEventRequest();
        request3.setFamilyId(testFamilyId);
        request3.setTitle("第二天事件");
        request3.setStartTime(sdf.parse("2025-07-06 16:00:00"));
        request3.setEndTime(sdf.parse("2025-07-06 17:00:00"));
        request3.setMemberIds(Arrays.asList(testMember1Id, testMember2Id));

        // 创建事件
        eventsService.createEvent(request1);
        eventsService.createEvent(request2);
        eventsService.createEvent(request3);

        // 测试按日期范围查询
        mockMvc.perform(get("/api/events/family/" + testFamilyId + "/date-range")
                .param("startDate", "2025-07-05")
                .param("endDate", "2025-07-07"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(3)) // 3天的数据
                .andExpect(jsonPath("$.data[0].date").value("2025-07-05"))
                .andExpect(jsonPath("$.data[0].eventCount").value(2))
                .andExpect(jsonPath("$.data[0].events.length()").value(2))
                .andExpect(jsonPath("$.data[1].date").value("2025-07-06"))
                .andExpect(jsonPath("$.data[1].eventCount").value(1))
                .andExpect(jsonPath("$.data[1].events.length()").value(1))
                .andExpect(jsonPath("$.data[2].date").value("2025-07-07"))
                .andExpect(jsonPath("$.data[2].eventCount").value(0))
                .andExpect(jsonPath("$.data[2].events.length()").value(0));
    }

    @Test
    public void testGetEventsByDateRangeWithInvalidParams() throws Exception {
        // 测试无效的家庭ID
        mockMvc.perform(get("/api/events/family/99999/date-range")
                .param("startDate", "2025-07-05")
                .param("endDate", "2025-07-07"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));

        // 测试无效的日期格式
        mockMvc.perform(get("/api/events/family/" + testFamilyId + "/date-range")
                .param("startDate", "invalid-date")
                .param("endDate", "2025-07-07"))
                .andExpect(status().isBadRequest());

        // 测试开始日期晚于结束日期
        mockMvc.perform(get("/api/events/family/" + testFamilyId + "/date-range")
                .param("startDate", "2025-07-10")
                .param("endDate", "2025-07-05"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));
    }

    @Test
    public void testGetEventsByDateRangeEmptyResult() throws Exception {
        // 查询没有事件的日期范围
        mockMvc.perform(get("/api/events/family/" + testFamilyId + "/date-range")
                .param("startDate", "2025-08-01")
                .param("endDate", "2025-08-03"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(3)) // 3天的数据
                .andExpect(jsonPath("$.data[0].eventCount").value(0))
                .andExpect(jsonPath("$.data[1].eventCount").value(0))
                .andExpect(jsonPath("$.data[2].eventCount").value(0));
    }

    @Test
    public void testGetEventsByDateRangeSingleDay() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 创建单天事件
        CreateEventRequest request = new CreateEventRequest();
        request.setFamilyId(testFamilyId);
        request.setTitle("单天事件");
        request.setStartTime(sdf.parse("2025-07-10 10:00:00"));
        request.setEndTime(sdf.parse("2025-07-10 11:00:00"));
        request.setMemberIds(Arrays.asList(testMember1Id));

        eventsService.createEvent(request);

        // 查询单天范围
        mockMvc.perform(get("/api/events/family/" + testFamilyId + "/date-range")
                .param("startDate", "2025-07-10")
                .param("endDate", "2025-07-10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].date").value("2025-07-10"))
                .andExpect(jsonPath("$.data[0].eventCount").value(1))
                .andExpect(jsonPath("$.data[0].events[0].title").value("单天事件"));
    }

    @Test
    public void testGetEventsByDateRangeMissingParams() throws Exception {
        // 测试缺少startDate参数
        mockMvc.perform(get("/api/events/family/" + testFamilyId + "/date-range")
                .param("endDate", "2025-07-07"))
                .andExpect(status().isBadRequest());

        // 测试缺少endDate参数
        mockMvc.perform(get("/api/events/family/" + testFamilyId + "/date-range")
                .param("startDate", "2025-07-05"))
                .andExpect(status().isBadRequest());
    }
}
