package com.barantt.familyclender.service;

import com.barantt.familyclender.dao.pojo.Events;
import com.barantt.familyclender.dao.pojo.Families;
import com.barantt.familyclender.dao.pojo.FamilyMembers;
import com.barantt.familyclender.dto.CreateEventRequest;
import com.barantt.familyclender.vo.EventsByDateVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 按日期范围查询事件测试类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class EventsByDateRangeTest {

    @Autowired
    private IEventsService eventsService;

    @Autowired
    private IFamiliesService familiesService;

    @Autowired
    private IFamilyMembersService familyMembersService;

    private Integer testFamilyId;
    private Integer testMember1Id;
    private Integer testMember2Id;

    @BeforeEach
    public void setUp() {
        // 创建测试家庭
        Families testFamily = familiesService.createFamily("测试家庭");
        testFamilyId = testFamily.getId();

        // 创建测试成员
        FamilyMembers member1 = familyMembersService.createFamilyMember(testFamilyId, "张三");
        FamilyMembers member2 = familyMembersService.createFamilyMember(testFamilyId, "李四");
        testMember1Id = member1.getId();
        testMember2Id = member2.getId();
    }

    @Test
    public void testGetEventsByDateRange() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // 创建不同日期的事件
        // 第一天的事件
        CreateEventRequest request1 = new CreateEventRequest();
        request1.setFamilyId(testFamilyId);
        request1.setTitle("第一天事件1");
        request1.setStartTime(sdf.parse("2025-07-05 09:00:00"));
        request1.setEndTime(sdf.parse("2025-07-05 10:00:00"));
        request1.setMemberIds(Arrays.asList(testMember1Id));

        CreateEventRequest request2 = new CreateEventRequest();
        request2.setFamilyId(testFamilyId);
        request2.setTitle("第一天事件2");
        request2.setStartTime(sdf.parse("2025-07-05 14:00:00"));
        request2.setEndTime(sdf.parse("2025-07-05 15:00:00"));
        request2.setMemberIds(Arrays.asList(testMember2Id));

        // 第二天的事件
        CreateEventRequest request3 = new CreateEventRequest();
        request3.setFamilyId(testFamilyId);
        request3.setTitle("第二天事件");
        request3.setStartTime(sdf.parse("2025-07-06 16:00:00"));
        request3.setEndTime(sdf.parse("2025-07-06 17:00:00"));
        request3.setMemberIds(Arrays.asList(testMember1Id, testMember2Id));

        // 第四天的事件（跳过第三天）
        CreateEventRequest request4 = new CreateEventRequest();
        request4.setFamilyId(testFamilyId);
        request4.setTitle("第四天事件");
        request4.setStartTime(sdf.parse("2025-07-08 18:00:00"));
        request4.setEndTime(sdf.parse("2025-07-08 19:00:00"));
        request4.setMemberIds(Arrays.asList(testMember1Id));

        // 创建事件
        eventsService.createEvent(request1);
        eventsService.createEvent(request2);
        eventsService.createEvent(request3);
        eventsService.createEvent(request4);

        // 查询日期范围内的事件
        SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = dateSdf.parse("2025-07-05");
        Date endDate = dateSdf.parse("2025-07-08");

        List<EventsByDateVO> result = eventsService.getEventsByDateRange(testFamilyId, startDate, endDate);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size()); // 应该包含4天的数据

        // 验证第一天（2025-07-05）
        EventsByDateVO day1 = result.get(0);
        assertEquals("2025-07-05", dateSdf.format(day1.getDate()));
        assertEquals(2, day1.getEventCount());
        assertEquals(2, day1.getEvents().size());

        // 验证第二天（2025-07-06）
        EventsByDateVO day2 = result.get(1);
        assertEquals("2025-07-06", dateSdf.format(day2.getDate()));
        assertEquals(1, day2.getEventCount());
        assertEquals(1, day2.getEvents().size());

        // 验证第三天（2025-07-07）- 没有事件
        EventsByDateVO day3 = result.get(2);
        assertEquals("2025-07-07", dateSdf.format(day3.getDate()));
        assertEquals(0, day3.getEventCount());
        assertEquals(0, day3.getEvents().size());

        // 验证第四天（2025-07-08）
        EventsByDateVO day4 = result.get(3);
        assertEquals("2025-07-08", dateSdf.format(day4.getDate()));
        assertEquals(1, day4.getEventCount());
        assertEquals(1, day4.getEvents().size());
    }

    @Test
    public void testGetEventsByDateRangeWithInvalidParams() throws Exception {
        SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = dateSdf.parse("2025-07-05");
        Date endDate = dateSdf.parse("2025-07-08");

        // 测试无效家庭ID
        assertThrows(IllegalArgumentException.class, () -> {
            eventsService.getEventsByDateRange(null, startDate, endDate);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            eventsService.getEventsByDateRange(0, startDate, endDate);
        });

        // 测试空日期
        assertThrows(IllegalArgumentException.class, () -> {
            eventsService.getEventsByDateRange(testFamilyId, null, endDate);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            eventsService.getEventsByDateRange(testFamilyId, startDate, null);
        });

        // 测试开始日期晚于结束日期
        assertThrows(IllegalArgumentException.class, () -> {
            eventsService.getEventsByDateRange(testFamilyId, endDate, startDate);
        });
    }

    @Test
    public void testGetEventsByDateRangeEmptyResult() throws Exception {
        SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = dateSdf.parse("2025-08-01");
        Date endDate = dateSdf.parse("2025-08-03");

        // 查询没有事件的日期范围
        List<EventsByDateVO> result = eventsService.getEventsByDateRange(testFamilyId, startDate, endDate);

        assertNotNull(result);
        assertEquals(3, result.size()); // 应该包含3天的数据

        // 验证每天都没有事件
        for (EventsByDateVO dayEvent : result) {
            assertEquals(0, dayEvent.getEventCount());
            assertEquals(0, dayEvent.getEvents().size());
        }
    }

    @Test
    public void testGetEventsByDateRangeSingleDay() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");

        // 创建单天事件
        CreateEventRequest request = new CreateEventRequest();
        request.setFamilyId(testFamilyId);
        request.setTitle("单天事件");
        request.setStartTime(sdf.parse("2025-07-10 10:00:00"));
        request.setEndTime(sdf.parse("2025-07-10 11:00:00"));
        request.setMemberIds(Arrays.asList(testMember1Id));

        eventsService.createEvent(request);

        // 查询单天范围
        Date singleDate = dateSdf.parse("2025-07-10");
        List<EventsByDateVO> result = eventsService.getEventsByDateRange(testFamilyId, singleDate, singleDate);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getEventCount());
        assertEquals("单天事件", result.get(0).getEvents().get(0).getTitle());
    }
}
