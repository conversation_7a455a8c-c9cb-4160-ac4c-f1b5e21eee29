<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.barantt.familyclender.dao.mapper.EventMembersMapper">

  <resultMap id="BaseResultMap" type="com.barantt.familyclender.dao.pojo.EventMembers">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="event_id" jdbcType="INTEGER" property="eventId"/>
    <result column="member_id" jdbcType="INTEGER" property="memberId"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, event_id, member_id, created_at
  </sql>

  <!-- 插入事件成员关系 -->
  <insert id="insertEventMember" parameterType="com.barantt.familyclender.dao.pojo.EventMembers" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO event_members (event_id, member_id, created_at)
    VALUES (#{eventId,jdbcType=INTEGER}, #{memberId,jdbcType=INTEGER}, #{createdAt,jdbcType=TIMESTAMP})
  </insert>

  <!-- 根据事件ID查询参与成员 -->
  <select id="selectByEventId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM event_members
    WHERE event_id = #{eventId,jdbcType=INTEGER}
    ORDER BY created_at ASC
  </select>

  <!-- 根据成员ID查询参与的事件 -->
  <select id="selectByMemberId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM event_members
    WHERE member_id = #{memberId,jdbcType=INTEGER}
    ORDER BY created_at ASC
  </select>

  <!-- 删除事件的所有参与者 -->
  <delete id="deleteByEventId" parameterType="java.lang.Integer">
    DELETE FROM event_members WHERE event_id = #{eventId,jdbcType=INTEGER}
  </delete>

  <!-- 删除成员的所有事件关系 -->
  <delete id="deleteByMemberId" parameterType="java.lang.Integer">
    DELETE FROM event_members WHERE member_id = #{memberId,jdbcType=INTEGER}
  </delete>
</mapper>
