<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.barantt.familyclender.dao.mapper.EventMembersMapper">

  <resultMap id="BaseResultMap" type="com.barantt.familyclender.dao.pojo.EventMembers">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="event_id" jdbcType="INTEGER" property="eventId"/>
    <result column="member_id" jdbcType="INTEGER" property="memberId"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, event_id, member_id, created_at
  </sql>
</mapper>
