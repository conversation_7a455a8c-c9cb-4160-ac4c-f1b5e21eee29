package com.barantt.familyclender.service.impl;

import com.barantt.familyclender.dao.mapper.FamiliesMapper;
import com.barantt.familyclender.dao.pojo.Families;
import com.barantt.familyclender.service.IFamiliesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
@Service
public class FamiliesServiceImpl implements IFamiliesService {

    @Autowired
    private FamiliesMapper familiesMapper;

    @Override
    public Families createFamily(String familyName) {
        // 参数校验
        if (!StringUtils.hasText(familyName)) {
            throw new IllegalArgumentException("家庭名称不能为空");
        }

        // 构建家庭对象
        Date now = new Date();
        Families family = Families.builder()
                .name(familyName.trim())
                .createdAt(now)
                .updatedAt(now)
                .build();

        // 保存到数据库
        int result = familiesMapper.insertSelective(family);
        if (result <= 0) {
            throw new RuntimeException("创建家庭失败");
        }

        return family;
    }
}
