package com.barantt.familyclender.controller;

import com.barantt.familyclender.dao.pojo.Families;
import com.barantt.familyclender.dao.pojo.FamilyMembers;
import com.barantt.familyclender.dto.CreateFamilyMemberRequest;
import com.barantt.familyclender.service.IFamiliesService;
import com.barantt.familyclender.service.IFamilyMembersService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 家庭成员控制器测试类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class FamilyMembersControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private IFamiliesService familiesService;

    @Autowired
    private IFamilyMembersService familyMembersService;

    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private Integer testFamilyId;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 创建测试家庭
        Families testFamily = familiesService.createFamily("测试家庭");
        testFamilyId = testFamily.getId();
    }

    @Test
    public void testCreateFamilyMember() throws Exception {
        CreateFamilyMemberRequest request = new CreateFamilyMemberRequest();
        request.setFamilyId(testFamilyId);
        request.setName("张三");

        mockMvc.perform(post("/family-members")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("家庭成员创建成功"))
                .andExpect(jsonPath("$.data.familyId").value(testFamilyId))
                .andExpect(jsonPath("$.data.name").value("张三"));
    }

    @Test
    public void testCreateFamilyMemberWithInvalidParams() throws Exception {
        // 测试空家庭ID
        CreateFamilyMemberRequest request1 = new CreateFamilyMemberRequest();
        request1.setFamilyId(null);
        request1.setName("张三");

        mockMvc.perform(post("/family-members")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));

        // 测试空成员姓名
        CreateFamilyMemberRequest request2 = new CreateFamilyMemberRequest();
        request2.setFamilyId(testFamilyId);
        request2.setName("");

        mockMvc.perform(post("/family-members")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));
    }

    @Test
    public void testDeleteFamilyMember() throws Exception {
        // 先创建一个家庭成员
        FamilyMembers familyMember = familyMembersService.createFamilyMember(testFamilyId, "李四");
        Integer memberId = familyMember.getId();

        // 测试删除
        mockMvc.perform(delete("/family-members/" + memberId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("家庭成员删除成功"))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void testDeleteNonExistentFamilyMember() throws Exception {
        // 测试删除不存在的成员
        mockMvc.perform(delete("/family-members/99999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));
    }

    @Test
    public void testGetFamilyMembersByFamilyId() throws Exception {
        // 创建多个家庭成员
        familyMembersService.createFamilyMember(testFamilyId, "张三");
        familyMembersService.createFamilyMember(testFamilyId, "李四");

        // 查询家庭成员列表
        mockMvc.perform(get("/family-members/family/" + testFamilyId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));
    }

    @Test
    public void testGetFamilyMemberById() throws Exception {
        // 创建一个家庭成员
        FamilyMembers familyMember = familyMembersService.createFamilyMember(testFamilyId, "王五");
        Integer memberId = familyMember.getId();

        // 根据ID查询
        mockMvc.perform(get("/family-members/" + memberId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.id").value(memberId))
                .andExpect(jsonPath("$.data.name").value("王五"));
    }

    @Test
    public void testGetNonExistentFamilyMember() throws Exception {
        // 查询不存在的成员
        mockMvc.perform(get("/family-members/99999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("家庭成员不存在"));
    }
}
