package com.barantt.familyclender.dao.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.barantt.familyclender.dao.pojo.Events;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.Date;
import java.util.List;

/**
*  Mapper
*
* <AUTHOR>
* @since 2025-07-02 17:35
*/
@Mapper
public interface EventsMapper extends BaseMapper<Events>, ExampleMapper<Events>  {

    /**
     * 根据家庭ID和时间范围查询事件
     *
     * @param familyId 家庭ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 事件列表
     */
    List<Events> selectByFamilyIdAndDateRange(@Param("familyId") Integer familyId,
                                              @Param("startDate") Date startDate,
                                              @Param("endDate") Date endDate);
}
