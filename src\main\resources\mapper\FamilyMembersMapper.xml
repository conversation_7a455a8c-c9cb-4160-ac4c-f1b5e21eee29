<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.barantt.familyclender.dao.mapper.FamilyMembersMapper">

  <resultMap id="BaseResultMap" type="com.barantt.familyclender.dao.pojo.FamilyMembers">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="family_id" jdbcType="INTEGER" property="familyId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, family_id, name, created_at
  </sql>

  <!-- 插入家庭成员信息 -->
  <insert id="insertFamilyMember" parameterType="com.barantt.familyclender.dao.pojo.FamilyMembers" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO family_members (family_id, name, created_at)
    VALUES (#{familyId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP})
  </insert>

  <!-- 根据家庭ID查询家庭成员 -->
  <select id="selectByFamilyId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM family_members
    WHERE family_id = #{familyId,jdbcType=INTEGER}
    ORDER BY created_at ASC
  </select>

  <!-- 根据成员姓名和家庭ID查询 -->
  <select id="selectByNameAndFamilyId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM family_members
    WHERE family_id = #{familyId,jdbcType=INTEGER}
    AND name = #{name,jdbcType=VARCHAR}
  </select>
</mapper>
