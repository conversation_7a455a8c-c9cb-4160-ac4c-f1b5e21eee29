<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.barantt.familyclender.dao.mapper.FamilyMembersMapper">

  <resultMap id="BaseResultMap" type="com.barantt.familyclender.dao.pojo.FamilyMembers">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="family_id" jdbcType="INTEGER" property="familyId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, family_id, name, created_at
  </sql>
</mapper>
