<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.barantt.familyclender.dao.mapper.EventsMapper">

  <resultMap id="BaseResultMap" type="com.barantt.familyclender.dao.pojo.Events">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="family_id" jdbcType="INTEGER" property="familyId"/>
    <result column="title" jdbcType="VARCHAR" property="title"/>
    <result column="location" jdbcType="VARCHAR" property="location"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.barantt.familyclender.dao.pojo.Events">
    <result column="description" jdbcType="LONGVARCHAR" property="description"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, family_id, title, location, start_time, end_time, created_at,
	updated_at
  </sql>
  <sql id="Blob_Column_List">
    description
  </sql>

  <!-- 插入事件信息 -->
  <insert id="insertEvent" parameterType="com.barantt.familyclender.dao.pojo.Events" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO events (family_id, title, description, location, start_time, end_time, created_at, updated_at)
    VALUES (#{familyId,jdbcType=INTEGER}, #{title,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR},
            #{location,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP},
            #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>

  <!-- 根据家庭ID查询事件列表 -->
  <select id="selectByFamilyId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    SELECT
    <include refid="Base_Column_List" />,
    <include refid="Blob_Column_List" />
    FROM events
    WHERE family_id = #{familyId,jdbcType=INTEGER}
    ORDER BY start_time ASC
  </select>

  <!-- 根据时间范围查询事件 -->
  <select id="selectByTimeRange" resultMap="ResultMapWithBLOBs">
    SELECT
    <include refid="Base_Column_List" />,
    <include refid="Blob_Column_List" />
    FROM events
    WHERE family_id = #{familyId,jdbcType=INTEGER}
    AND start_time &gt;= #{startTime,jdbcType=TIMESTAMP}
    AND end_time &lt;= #{endTime,jdbcType=TIMESTAMP}
    ORDER BY start_time ASC
  </select>

  <!-- 根据家庭ID和日期范围查询事件 -->
  <select id="selectByFamilyIdAndDateRange" resultMap="ResultMapWithBLOBs">
    SELECT
    <include refid="Base_Column_List" />,
    <include refid="Blob_Column_List" />
    FROM events
    WHERE family_id = #{familyId,jdbcType=INTEGER}
    AND DATE(start_time) &gt;= DATE(#{startDate,jdbcType=TIMESTAMP})
    AND DATE(start_time) &lt;= DATE(#{endDate,jdbcType=TIMESTAMP})
    ORDER BY start_time ASC
  </select>
</mapper>
