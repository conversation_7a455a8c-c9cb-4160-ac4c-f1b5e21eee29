<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.barantt.familyclender.dao.mapper.EventsMapper">

  <resultMap id="BaseResultMap" type="com.barantt.familyclender.dao.pojo.Events">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="family_id" jdbcType="INTEGER" property="familyId"/>
    <result column="title" jdbcType="VARCHAR" property="title"/>
    <result column="location" jdbcType="VARCHAR" property="location"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.barantt.familyclender.dao.pojo.Events">
    <result column="description" jdbcType="LONGVARCHAR" property="description"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, family_id, title, location, start_time, end_time, created_at, 
	updated_at
  </sql>
  <sql id="Blob_Column_List">
    description
  </sql>
</mapper>
