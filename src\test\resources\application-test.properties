# 测试环境配置
server.port=8081

# 数据库配置（使用H2内存数据库进行测试）
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=

# H2数据库控制台
spring.h2.console.enabled=true

# MyBatis配置
mybatis.mapper-locations=classpath:mapper/*xml
mybatis.type-aliases-package=com.barantt.familyclender.dao.pojo

# 日志配置
logging.level.com.barantt.familyclender.dao.mapper=DEBUG
logging.level.org.springframework.web=DEBUG

# 初始化数据库
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
