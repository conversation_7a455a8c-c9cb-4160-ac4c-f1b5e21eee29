package com.barantt.familyclender.service;

import com.barantt.familyclender.dao.pojo.Families;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 家庭服务测试类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@SpringBootTest
@ActiveProfiles("test")
public class FamiliesServiceTest {

    @Autowired
    private IFamiliesService familiesService;

    @Test
    public void testCreateFamily() {
        // 测试正常创建家庭
        String familyName = "测试家庭";
        Families family = familiesService.createFamily(familyName);
        
        assertNotNull(family);
        assertNotNull(family.getId());
        assertEquals(familyName, family.getName());
        assertNotNull(family.getCreatedAt());
        assertNotNull(family.getUpdatedAt());
    }

    @Test
    public void testCreateFamilyWithEmptyName() {
        // 测试空名称
        assertThrows(IllegalArgumentException.class, () -> {
            familiesService.createFamily("");
        });
        
        // 测试null名称
        assertThrows(IllegalArgumentException.class, () -> {
            familiesService.createFamily(null);
        });
        
        // 测试空白名称
        assertThrows(IllegalArgumentException.class, () -> {
            familiesService.createFamily("   ");
        });
    }
}
