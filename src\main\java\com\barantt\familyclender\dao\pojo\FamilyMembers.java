package com.barantt.familyclender.dao.pojo;

import java.util.Date;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @since 2025-07-02 17:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilyMembers {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private Integer familyId;

    private String name;

    private Date createdAt;
}
